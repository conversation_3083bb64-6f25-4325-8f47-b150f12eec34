"use client";

import type { GameData } from "@/types";

// This is a mock implementation of the Goama SDK for local development.
// It uses localStorage to simulate the platform's data storage.

const GAMA_DATA_KEY = 'OrangeQuest-gamedata';

const mockSDK = {
  gameStarted: () => {
    console.log("SDK: gameStarted (gameLoaded) called");
  },

  getGameData: (defaultData: GameData, callback: (data: GameData) => void) => {
    console.log("SDK: getGameData called");
    try {
      const storedData = localStorage.getItem(GAMA_DATA_KEY);
      if (storedData) {
        callback(JSON.parse(storedData));
      } else {
        callback(defaultData);
      }
    } catch (error) {
      console.error("Error getting game data from localStorage", error);
      callback(defaultData);
    }
  },

  saveGameData: (dataToSave: any) => {
    console.log("SDK: saveGameData called with", dataToSave);
    // This mock saves the full game data to preserve progress during development.
    // The passed `dataToSave` is what would be sent to the real SDK.
    try {
      const currentFullData = localStorage.getItem(GAMA_DATA_KEY);
      if(currentFullData) {
        const parsed = JSON.parse(currentFullData);
        // We update the high score from the main game state, not the SDK payload,
        // because the SDK payload score is the current turn count.
        const newHighScore = Math.max(parsed.highScore || 0, dataToSave.score);
        parsed.highScore = newHighScore;
        localStorage.setItem(GAMA_DATA_KEY, JSON.stringify(parsed));
      }
    } catch (error) {
      console.error("Error saving game data to localStorage", error);
    }
  },

  gameOver: (score: number) => {
    console.log(`SDK: gameOver called with score: ${score}`);
    // In a real scenario, this would notify the platform.
    // We can also update the high score in our mock data.
    mockSDK.getGameData({} as GameData, (data) => {
        const newHighScore = Math.max(data.highScore || 0, score);
        mockSDK.saveGameData({ ...data, highScore: newHighScore, score: score });
    });
  },

  gamePaused: () => {
    console.log("SDK: gamePaused called");
  },
  
  gameResumed: () => {
    console.log("SDK: gameResumed called");
  },

  listenPaused: (callback: () => void) => {
    console.log("SDK: listenPaused registered");
    // Mock pause event with a key press (e.g., 'P')
    const handlePause = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'p') {
        console.log("SDK: Mock pause event triggered");
        callback();
      }
    };
    window.addEventListener('keydown', handlePause);
    return () => window.removeEventListener('keydown', handlePause);
  },

  listenResumed: (callback: () => void) => {
    console.log("SDK: listenResumed registered");
    // Mock resume event with a key press (e.g., 'R')
    const handleResume = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'r') {
        console.log("SDK: Mock resume event triggered");
        callback();
      }
    };
    window.addEventListener('keydown', handleResume);
    return () => window.removeEventListener('keydown', handleResume);
  },

  listenQuit: (callback: () => void) => {
    console.log("SDK: listenQuit registered");
     // Mock quit event with a key press (e.g., 'Q')
    const handleQuit = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'q') {
        console.log("SDK: Mock quit event triggered");
        callback();
      }
    };
    window.addEventListener('keydown', handleQuit);
    return () => window.removeEventListener('keydown', handleQuit);
  }
};


// In a real build, you might use a build flag to switch between mock and real SDK.
export const SDK = mockSDK;
