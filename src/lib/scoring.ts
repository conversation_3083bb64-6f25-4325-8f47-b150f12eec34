import type { GameData } from '@/types';

export const calculateScore = (data: GameData): number => {
    if (!data.player) return 0;
    
    let score = 0;

    // Resources are a direct part of the score
    score += data.resources.credits;

    // Value for ship components and cargo
    score += data.ship.weapons.length * 100;
    score += data.ship.cargo.length * 50;
    score += data.ship.upgrades.length * 150;

    // Value for each crew member
    score += data.ship.crew.length * 200;

    // Bonus for ship integrity
    score += Math.floor(data.ship.hull.current * 2);
    score += Math.floor(data.ship.shields.current * 1);

    // High score gets a bonus
    score += data.highScore > 0 ? Math.floor(data.highScore * 0.1) : 0;
    
    // Penalize for turns taken - encourages efficiency
    const turnPenalty = data.gameTurn * 10;
    score -= turnPenalty;

    // Ensure score is never negative
    return Math.max(0, Math.floor(score));
}
