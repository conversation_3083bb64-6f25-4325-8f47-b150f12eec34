import type { Race, Starmap, WeaponSystem, Engine, ShieldGenerator, CrewMember, Discovery, Artifact, StarSystem, Planet } from '@/types';

export const RACES: Race[] = [
  {
    id: 'human',
    name: 'Human',
    description: 'Versatile and adaptable, humans are the jack-of-all-trades of the galaxy.',
    bonus: '+10% to all crew skills.',
  },
  {
    id: 'alien',
    name: '<PERSON><PERSON><PERSON>',
    description: 'An ancient and wise species with a deep connection to technology.',
    bonus: 'Start with advanced shield technology.',
  },
  {
    id: 'robot',
    name: 'C-Unit',
    description: 'Logic-driven automatons, resilient and efficient.',
    bonus: 'Ship requires 25% less fuel.',
  },
];

// --- ARTIFACTS ---
export const ARTIFACT_POWER_CELL: Artifact = {
  id: 'art_power_cell_1',
  name: 'Alien Power Cell',
  description: 'A strange, humming power source of unknown origin. It seems to integrate with your ship\'s power grid.',
  type: 'artifact',
  effectDescription: 'Increases ship max power by 20 when installed.',
  powerDraw: -20, // It provides power
  mass: 5,
  bonus: { stat: 'power.max', value: 20, operator: 'add' }
};

export const ARTIFACT_NAV_MATRIX: Artifact = {
  id: 'art_nav_matrix_1',
  name: 'Crystalline Nav-Matrix',
  description: 'A crystal lattice that seems to map hyperspace lanes before they are even calculated.',
  type: 'artifact',
  effectDescription: 'Reduces fuel consumption for all travel by 1 unit.',
  powerDraw: 2,
  mass: 2,
  bonus: { stat: 'fuelConsumption', value: -1, operator: 'add' }
};

const SYSTEM_PREFIXES = ["Kepler", "Gliese", "TRAPPIST", "Proxima", "Wolf", "Luyten", "Tau", "Cygnus", "Orion", "Pegasus"];
const SYSTEM_SUFFIXES = ["Prime", "Major", "Minor", "Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta", "Omega"];

const PLANET_NAMES = ["Terra", "Gaia", "Aether", "Xylos", "Helios", "Krypton", "Argon", "Neon", "Zephyr", "Aura", "Chronos", "Rhea", "Janus", "Mimas", "Hyperion"];
const PLANET_DESCRIPTIONS = [
    "A terrestrial world with sprawling continents and deep blue oceans.",
    "A volcanic hothouse, its surface obscured by thick, sulfuric clouds.",
    "An ice-covered planet with cryogeysers erupting into the thin atmosphere.",
    "A gas giant with turbulent storms and a faint, shimmering ring system.",
    "A barren, crater-pocked rock, stripped of its atmosphere by solar winds.",
    "A jungle world teeming with exotic, and possibly dangerous, flora and fauna.",
    "A tidally-locked planet with one side in perpetual daylight and the other in eternal night.",
    "A desert planet with vast dune seas and towering rock formations.",
    "A world with a global ocean, dotted with archipelagos and volcanic islands.",
    "A planet with extreme axial tilt, resulting in chaotic and unpredictable seasons."
];
const PLANET_COLORS = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#F7B801", "#5F4B8B", "#E67E22", "#9B59B6", "#3498DB", "#1ABC9C", "#F1C40F"];


const shuffleArray = (array: any[]) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

const POSSIBLE_DISCOVERIES: Discovery[] = [
    { type: 'credits', value: 500, description: 'You find a derelict freighter with its cargo hold intact, full of valuable credits.' },
    { type: 'artifact', value: ARTIFACT_POWER_CELL, description: 'A strange signal leads you to a non-human probe containing an unknown device.' },
    { type: 'enemy', value: { difficulty: 'easy' }, description: 'It\'s a trap! A pirate squadron warps in to ambush you.' },
    { type: 'nothing', value: null, description: 'The sensor readings were just a cluster of asteroids. There is nothing of value here.'},
    { type: 'fuel', value: 50, description: 'You find a deposit of refined fuel canisters floating in orbit.' },
    { type: 'artifact', value: ARTIFACT_NAV_MATRIX, description: 'Buried on a moonlet, you find a crystalline shard humming with navigational data.'},
    { type: 'enemy', value: { difficulty: 'medium' }, description: 'A territorial species guards this planet and attacks on sight.'},
    { type: 'nothing', value: null, description: 'A solar flare corrupted your sensor data. There\'s nothing here.'},
    { type: 'outpost', value: { shop: true, fuel: true }, description: 'You discover a bustling trading outpost, a rare sight in this sector.' },
    { type: 'enemy', value: { difficulty: 'hard' }, description: 'A massive pirate capital ship is using the planet as a staging ground.'},
    { type: 'fuel', value: 25, description: 'A small, automated refueling station has a surplus you can take.'},
    { type: 'credits', value: 750, description: 'You stumble upon a smuggler\'s hidden cache, ripe for the picking.'},
];


export const generateStarmap = (width: number, height: number): Starmap => {
    const systems: StarSystem[] = [];
    let discoveryPool = shuffleArray(POSSIBLE_DISCOVERIES);

    const getDiscovery = () => {
        if (discoveryPool.length === 0) {
            discoveryPool = shuffleArray(POSSIBLE_DISCOVERIES);
        }
        return discoveryPool.pop() || { type: 'nothing', value: null, description: 'This system appears to be empty.' };
    }

    for (let i = 0; i < 10; i++) {
        const systemId = `system-${i}`;
        const system: StarSystem = {
            id: systemId,
            name: `${SYSTEM_PREFIXES[Math.floor(Math.random() * SYSTEM_PREFIXES.length)]}-${Math.floor(Math.random() * 900) + 100}`,
            x: Math.random() * width * 0.8 + width * 0.1, // Add padding
            y: Math.random() * height * 0.8 + height * 0.1, // Add padding
            size: Math.random() * 10 + 8,
            color: PLANET_COLORS[i % PLANET_COLORS.length],
            planets: [],
        };

        const numPlanets = Math.floor(Math.random() * 4) + 1; // 1 to 4 planets per system
        for (let j = 0; j < numPlanets; j++) {
            const planet: Planet = {
                id: `planet-${i}-${j}`,
                systemId: systemId,
                name: `${system.name} ${String.fromCharCode(98 + j)}`, // b, c, d...
                description: PLANET_DESCRIPTIONS[Math.floor(Math.random() * PLANET_DESCRIPTIONS.length)],
                image: `https://placehold.co/1200x800.png`,
                hasBeenExplored: false,
                discovery: getDiscovery(),
            };
            system.planets.push(planet);
        }
        systems.push(system);
    }
    return { systems };
};


// --- DEFAULT SHIP COMPONENTS ---

export const DEFAULT_WEAPON: WeaponSystem = {
  id: 'w_laser_1',
  name: 'Standard Laser Cannon',
  description: 'A reliable, basic energy weapon. Standard issue on most civilian and light military vessels.',
  type: 'weapon',
  damage: 15,
  range: 5,
  powerDraw: 10,
  mass: 5,
};

export const WEAPON_PULSE_LASER: WeaponSystem = {
  id: 'w_pulse_laser_1',
  name: 'Pulse Laser',
  description: 'A rapid-fire laser with a shorter range but higher damage output.',
  type: 'weapon',
  damage: 20,
  range: 3,
  powerDraw: 15,
  mass: 6,
};


export const DEFAULT_ENGINE: Engine = {
  id: 'e_drive_1',
  name: 'Class-1 Fusion Drive',
  description: 'A common civilian-grade engine. Balances thrust and fuel efficiency for interstellar travel.',
  type: 'engine',
  thrust: 4, // This will be the movement range in battle
  fuelConsumption: 10, // Higher is more efficient. Fuel cost = distance / fuelConsumption.
  powerDraw: 20,
  mass: 20,
};

export const DEFAULT_SHIELD: ShieldGenerator = {
  id: 's_deflector_1',
  name: 'Mark-I Deflector Array',
  description: 'Basic shield generator that provides a minimal layer of protection against space debris and light arms fire.',
  type: 'shield',
  capacity: 100,
  rechargeRate: 10, // 10 shield points per turn
  powerDraw: 15,
  mass: 10,
};


// --- DEFAULT CREW ---
export const DEFAULT_CREW: CrewMember[] = [
    {
        id: 'crew_1',
        name: 'Aria "Ace" Valerius',
        role: 'Pilot',
        skillLevel: 3,
        description: 'A former racer from the orbital rings of Cygnus X-1, Aria can make any ship dance.',
        bonusEffect: '+1 to ship movement range (Thrust).'
    },
    {
        id: 'crew_2',
        name: 'Dr. Aris Thorne',
        role: 'Engineer',
        skillLevel: 2,
        description: 'A meticulous engineer who sees ship systems as a symphony. Nothing is ever out of tune on his watch.',
        bonusEffect: '+10% to shield recharge rate.'
    },
    {
        id: 'crew_3',
        name: 'Kaelen "Deadeye"',
        role: 'Gunner',
        skillLevel: 2,
        description: 'A stoic veteran of the Outer Rim conflicts. They say he can shoot the leg off a space-fly from a parsec away.',
        bonusEffect: '+5% to weapon accuracy.'
    }
];
