"use client";

import { useState, useEffect, useCallback } from 'react';
import type { GameState, GameData, Player, StarSystem } from '@/types';
import { SDK } from '@/lib/goama-sdk';
import CharacterCreationScreen from './screens/CharacterCreationScreen';
import StarmapScreen from './screens/StarmapScreen';
import BattleScreen from './screens/BattleScreen';
import PlanetScreen from './screens/PlanetScreen';
import ShipManagementScreen from './screens/ShipManagementScreen';
import SystemViewScreen from './screens/SystemViewScreen';
import { useToast } from '@/hooks/use-toast';
import { Rocket } from 'lucide-react';
import { generateStarmap, DEFAULT_ENGINE, DEFAULT_SHIELD, DEFAULT_WEAPON, DEFAULT_CREW, WEAPON_PULSE_LASER } from '@/lib/constants';
import { calculateScore } from '@/lib/scoring';

const defaultStarmap = generateStarmap(1920, 1080);

const defaultGameData: GameData = {
  player: null,
  ship: {
    hull: { current: 100, max: 100 },
    shields: { current: 100, max: 100 },
    fuel: { current: 100, max: 100 },
    power: { current: 50, max: 50 },
    engine: DEFAULT_ENGINE,
    shieldGenerator: DEFAULT_SHIELD,
    weapons: [DEFAULT_WEAPON],
    crew: DEFAULT_CREW,
    cargo: [WEAPON_PULSE_LASER],
    upgrades: [],
  },
  highScore: 0,
  resources: {
    credits: 1000,
  },
  starmap: defaultStarmap,
  currentSystemId: defaultStarmap.systems[0].id,
  currentPlanetId: null,
  gameTurn: 0,
  achievements: {},
};

export default function GameContainer() {
  const [gameState, setGameState] = useState<GameState>('loading');
  const [gameData, setGameData] = useState<GameData>(defaultGameData);
  const [isPausedByPlatform, setIsPausedByPlatform] = useState(false);
  const { toast } = useToast();

  const getGameDataForSDK = (data: GameData) => {
    if (!data.player) return null;
    
    const { ship, gameTurn } = data;
    const attackPower = ship.weapons.reduce((acc, w) => acc + w.damage, 0);

    return {
        score: calculateScore(data),
        gameTurn: gameTurn,
        playerShipStats: {
            hull: ship.hull.current / ship.hull.max,
            shields: ship.shields.current / ship.shields.max,
            attackPower: attackPower,
        },
        achievements: data.achievements,
    };
  }

  const handleGameOver = useCallback((message: {title: string, description: string}) => {
    const sdkData = getGameDataForSDK(gameData);
    if (sdkData) {
        SDK.gameOver(sdkData.score);
        SDK.saveGameData(sdkData);
    }
    toast({ ...message, variant: "destructive", duration: 5000 });
    
    // Reset to start a new game after a delay to allow the user to read the message
    setTimeout(() => {
        const newStarmap = generateStarmap(1920, 1080);
        setGameState('character-creation');
        setGameData({ ...defaultGameData, starmap: newStarmap, currentSystemId: newStarmap.systems[0].id });
    }, 5000);
  }, [gameData, toast]);

  const handleQuit = useCallback(() => {
    const sdkData = getGameDataForSDK(gameData);
    if (sdkData) {
      SDK.saveGameData(sdkData);
      SDK.gameOver(sdkData.score);
    }
    toast({ title: "Game Over", description: `You have retired. Final score: ${sdkData?.score ?? 0}` });
    const newStarmap = generateStarmap(1920, 1080);
    setGameState('character-creation');
    setGameData({ ...defaultGameData, starmap: newStarmap, currentSystemId: newStarmap.systems[0].id });
  }, [gameData, toast]);

  // Effect for initialization, runs once on mount
  useEffect(() => {
    SDK.getGameData(defaultGameData, (data) => {
      if (!data.starmap || data.starmap.systems.length === 0) {
        data.starmap = generateStarmap(1920, 1080);
      }
      if (!data.ship) data.ship = defaultGameData.ship;
      if (!data.ship.hull) data.ship.hull = defaultGameData.ship.hull;
      if (!data.ship.shields) data.ship.shields = defaultGameData.ship.shields;
      if (!data.ship.fuel) data.ship.fuel = defaultGameData.ship.fuel;
      if (!data.ship.power) data.ship.power = defaultGameData.ship.power;
      if (!data.ship.engine) data.ship.engine = DEFAULT_ENGINE;
      if (!data.ship.shieldGenerator) data.ship.shieldGenerator = DEFAULT_SHIELD;
      if (!data.ship.weapons || data.ship.weapons.length === 0) data.ship.weapons = [DEFAULT_WEAPON];
      if (!data.ship.crew) data.ship.crew = DEFAULT_CREW;
      if (!data.ship.cargo) data.ship.cargo = [WEAPON_PULSE_LASER];
      if (data.gameTurn === undefined) data.gameTurn = 0;
      if (data.achievements === undefined) data.achievements = {};
      if (data.currentSystemId === undefined || data.currentSystemId === null) {
          data.currentSystemId = data.starmap.systems[0]?.id;
      }
      if(data.currentPlanetId === undefined) data.currentPlanetId = null;
      
      setGameData(data);
      if (data.player) {
        setGameState('starmap');
      } else {
        setGameState('character-creation');
      }
      SDK.gameStarted();
    });
  }, []);

  // Effect for listeners, re-runs when handleQuit or toast changes
  useEffect(() => {
    const cleanupPaused = SDK.listenPaused(() => {
        toast({ title: "Game Paused by Platform" });
        setIsPausedByPlatform(true);
    });
    const cleanupResume = SDK.listenResumed(() => {
        toast({ title: "Game Resumed", description: "Welcome back, Captain!" });
        setIsPausedByPlatform(false);
    });
    
    const cleanupQuit = SDK.listenQuit(handleQuit);

    return () => {
        cleanupPaused();
        cleanupResume();
        cleanupQuit();
    }
  }, [handleQuit, toast]);

  const handleCharacterCreation = (playerData: Player) => {
    const newStarmap = generateStarmap(1920, 1080);
    const newData = { ...defaultGameData, player: playerData, starmap: newStarmap, currentSystemId: newStarmap.systems[0].id, currentPlanetId: null };
    localStorage.setItem('OrangeQuest-gamedata', JSON.stringify(newData));
    const sdkData = getGameDataForSDK(newData);
    if (sdkData) {
      SDK.saveGameData(sdkData);
    }
    setGameData(newData);
    setGameState('starmap');
    toast({
      title: `Welcome, Captain ${playerData?.name}!`,
      description: `Your ship, the ${playerData?.shipName}, is ready.`,
    });
  };

  const renderScreen = () => {
    switch (gameState) {
      case 'character-creation':
        return <CharacterCreationScreen onComplete={handleCharacterCreation} />;
      case 'starmap':
        return <StarmapScreen gameData={gameData} setGameData={setGameData} setGameState={setGameState} handleQuit={handleQuit} isPausedByPlatform={isPausedByPlatform} />;
      case 'system-view':
        return <SystemViewScreen gameData={gameData} setGameData={setGameData} setGameState={setGameState} />;
      case 'battle':
        return <BattleScreen gameData={gameData} setGameData={setGameData} setGameState={setGameState} onGameOver={handleGameOver} />;
      case 'planet':
        return <PlanetScreen gameData={gameData} setGameData={setGameData} setGameState={setGameState} />;
      case 'ship-management':
        return <ShipManagementScreen gameData={gameData} setGameData={setGameData} setGameState={setGameState} />;
      case 'loading':
      default:
        return (
          <div className="flex h-full w-full items-center justify-center flex-col gap-4">
             <Rocket className="h-16 w-16 animate-pulse text-primary" />
            <p className="text-xl font-headline tracking-widest">LOADING STARSHIP VOYAGER...</p>
          </div>
        );
    }
  };

  return <div className="h-full w-full">{renderScreen()}</div>;
}
