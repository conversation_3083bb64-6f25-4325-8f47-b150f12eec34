
"use client";

import type { GameData, GameState, Planet, StarSystem } from '@/types';
import type { Dispatch, SetStateAction } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { ArrowLeft, CheckCircle, HelpCircle, Skull } from 'lucide-react';
import Image from 'next/image';

type SystemViewScreenProps = {
  gameData: GameData;
  setGameData: Dispatch<SetStateAction<GameData>>;
  setGameState: (state: GameState) => void;
};

const getDiscoveryIcon = (planet: Planet) => {
    if (planet.hasBeenExplored) {
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    switch (planet.discovery.type) {
        case 'enemy': return <Skull className="h-4 w-4 text-red-500" />;
        default: return <HelpCircle className="h-4 w-4 text-yellow-500" />;
    }
}

export default function SystemViewScreen({ gameData, setGameData, setGameState }: SystemViewScreenProps) {
  const currentSystem = gameData.starmap.systems.find(s => s.id === gameData.currentSystemId);

  if (!currentSystem) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <p>Error: System not found. Returning to starmap...</p>
        <Button onClick={() => setGameState('starmap')}>Go Back</Button>
      </div>
    );
  }

  const handlePlanetSelect = (planet: Planet) => {
    setGameData(prev => ({
        ...prev,
        currentPlanetId: planet.id,
    }));
    setGameState('planet');
  };

  return (
    <div className="h-full w-full bg-background p-8 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50" />
      <Card className="w-full max-w-4xl z-10 bg-card/80 backdrop-blur-sm">
        <CardHeader>
            <Button variant="outline" size="sm" className="absolute top-4 left-4" onClick={() => setGameState('starmap')}>
                <ArrowLeft className="mr-2 h-4 w-4"/>
                Back to Starmap
            </Button>
          <CardTitle className="text-4xl font-headline text-primary text-center pt-8">{currentSystem.name} System</CardTitle>
          <CardDescription className="text-center">Select a celestial body to approach.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
          {currentSystem.planets.map(planet => (
            <Card 
                key={planet.id} 
                className="hover:border-primary transition-colors cursor-pointer flex flex-col"
                onClick={() => handlePlanetSelect(planet)}
            >
                <CardHeader className="flex-shrink-0">
                    <CardTitle className="flex items-center justify-between">
                        <span>{planet.name}</span>
                        {getDiscoveryIcon(planet)}
                    </CardTitle>
                </CardHeader>
                <CardContent className="flex-grow flex flex-col justify-between">
                    <Image src={planet.image} alt={planet.name} width={400} height={300} className="rounded-md mb-2" data-ai-hint="planet surface" />
                    <p className="text-sm text-muted-foreground">{planet.description}</p>
                </CardContent>
                <CardFooter>
                    <Button variant="secondary" className="w-full">Approach Planet</Button>
                </CardFooter>
            </Card>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
