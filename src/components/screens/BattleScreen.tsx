"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import Image from 'next/image';
import type { GameData, GameState, BattleShip, BattleWeapon, Ship } from '@/types';
import { But<PERSON> } from '../ui/button';
import { Progress } from '../ui/progress';
import { motion, AnimatePresence } from 'framer-motion';
import { Undo2, Pause, Play, FastForward, Swords, Shield as ShieldIcon, Heart, Move } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

const GRID_SIZE = 12; // 12x12 grid

// --- SUB-COMPONENTS ---

const AlienIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M85.33,42.47c0,18-15.82,32.53-35.33,32.53S14.67,60.47,14.67,42.47S30.48,9.94,50,9.94S85.33,24.47,85.33,42.47Z" fill="#D32F2F"/>
        <path d="M50,89.5c-14.3,0-26.6-8.3-31.1-20.5H81.1C76.6,81.2,64.3,89.5,50,89.5Z" fill="#555"/>
        <ellipse cx="35" cy="45" rx="8" ry="12" fill="black"/>
        <ellipse cx="65"cy="45" rx="8" ry="12" fill="black"/>
    </svg>
);

const BattleHeader = ({ onRetreat }: { onRetreat: () => void }) => (
    <header className="absolute top-0 left-0 right-0 p-2 bg-black/30 backdrop-blur-sm z-20">
        <div className="flex items-center justify-between">
            <Button variant="outline" size="sm" className="h-8 border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 hover:text-yellow-300" onClick={onRetreat}>
                <Undo2 className="mr-2 h-4 w-4"/>
                Retreat
            </Button>
            <div className="flex items-center gap-1">
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-white/10" disabled><Pause className="h-5 w-5"/></Button>
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-white/10" disabled><Play className="h-5 w-5"/></Button>
                <Button variant="ghost" size="icon" className="text-gray-400 hover:text-white hover:bg-white/10" disabled><FastForward className="h-5 w-5"/></Button>
            </div>
            <AlienIcon className="h-12 w-12" />
        </div>
    </header>
);

const ShipStatusOverlay = ({ ship }: { ship: BattleShip | null }) => {
    if (!ship) return null;
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute p-2 rounded-lg bg-black/70 backdrop-blur-md border border-gray-700 w-48 text-white text-xs z-10"
            style={{
                top: `${(ship.position.y / (GRID_SIZE + 1)) * 100}%`,
                left: `${(ship.position.x / (GRID_SIZE + 1)) * 100}%`,
                transform: ship.position.x > GRID_SIZE / 2 ? 'translateX(-110%)' : 'translateX(10%)'
            }}
        >
            <p className="font-headline text-base">{ship.name}</p>
            <div className="space-y-1 mt-1">
                <div className="flex items-center gap-1">
                    <Heart className="h-3 w-3 text-red-500"/>
                    <Progress value={(ship.health / ship.maxHealth) * 100} className="h-1.5" />
                </div>
                <div className="flex items-center gap-1">
                    <ShieldIcon className="h-3 w-3 text-blue-400"/>
                    <Progress value={(ship.shields / ship.maxShields) * 100} className="h-1.5 [&>div]:bg-blue-400" />
                </div>
            </div>
        </motion.div>
    );
}

const BattleControls = ({ onEndTurn, onAttack, onMove, isPlayerTurn, playerAp, selectedAction }: { onEndTurn: () => void, onAttack: () => void, onMove: () => void, isPlayerTurn: boolean, playerAp: number, selectedAction: 'move' | 'attack' | null }) => (
    <footer className="absolute bottom-0 left-0 right-0 p-2 bg-black/30 backdrop-blur-sm z-20">
        <div className="flex justify-center items-center gap-4">
             <Button onClick={onMove} disabled={!isPlayerTurn || playerAp < 1} variant={selectedAction === 'move' ? 'default' : 'secondary'} size="lg">
                <Move className="mr-2"/> Move
            </Button>
            <Button onClick={onAttack} disabled={!isPlayerTurn || playerAp < 1} variant={selectedAction === 'attack' ? 'destructive' : 'secondary'} size="lg">
                <Swords className="mr-2"/> Fire Weapons
            </Button>
            <Button onClick={onEndTurn} disabled={!isPlayerTurn} variant="outline" size="lg">End Turn</Button>
        </div>
        {isPlayerTurn && <p className="absolute right-4 bottom-4 text-primary font-bold text-2xl font-headline">{playerAp} AP</p>}
    </footer>
);


// --- MAIN BATTLE SCREEN COMPONENT ---

export default function BattleScreen({ gameData, setGameState, setGameData, onGameOver }: { gameData: GameData, setGameState: (state: GameState) => void, setGameData: (data: GameData) => void, onGameOver: (message: {title: string, description: string}) => void }) {
    const { toast } = useToast();
    const [playerShip, setPlayerShip] = useState<BattleShip | null>(null);
    const [enemyShip, setEnemyShip] = useState<BattleShip | null>(null);
    const [hoveredShip, setHoveredShip] = useState<BattleShip | null>(null);
    const [turn, setTurn] = useState<'player' | 'enemy'>('player');
    const [log, setLog] = useState<string[]>([]);
    const [battleTurns, setBattleTurns] = useState(0);
    const [selectedAction, setSelectedAction] = useState<'move' | 'attack' | null>(null);
    const [highlightedTiles, setHighlightedTiles] = useState<{x: number, y: number}[]>([]);
    const [attackRangeTiles, setAttackRangeTiles] = useState<{x: number, y: number}[]>([]);
    const [attackableTargets, setAttackableTargets] = useState<string[]>([]);

    const ships = useMemo(() => [playerShip, enemyShip].filter(Boolean) as BattleShip[], [playerShip, enemyShip]);

    const getValidMoves = useCallback((ship: BattleShip, otherShip: BattleShip) => {
        const validMoves: {x: number, y: number}[] = [];
        if (!ship) return validMoves;
        const { x, y } = ship.position;
        const range = ship.movementRange;

        for (let i = 1; i <= GRID_SIZE; i++) {
            for (let j = 1; j <= GRID_SIZE; j++) {
                const dist = Math.abs(x - i) + Math.abs(y - j);
                if (dist > 0 && dist <= range) {
                    if (i !== otherShip.position.x || j !== otherShip.position.y) {
                        validMoves.push({ x: i, y: j });
                    }
                }
            }
        }
        return validMoves;
    }, []);

    const getAttackableTiles = useCallback((ship: BattleShip) => {
        const tiles: {x: number, y: number}[] = [];
        if (!ship || ship.weapons.length === 0) return tiles;
        const { x, y } = ship.position;
        const range = ship.weapons[0].range;

        for (let i = 1; i <= GRID_SIZE; i++) {
            for (let j = 1; j <= GRID_SIZE; j++) {
                const dist = Math.abs(x - i) + Math.abs(y - j);
                if (dist > 0 && dist <= range) {
                    tiles.push({ x: i, y: j });
                }
            }
        }
        return tiles;
    }, []);

    const handleBattleEnd = useCallback((didPlayerWin: boolean) => {
        let finalGameData = { ...gameData, gameTurn: gameData.gameTurn + battleTurns };
        
        if (didPlayerWin) {
            const creditsWon = Math.floor(Math.random() * 250) + 100;
            const fuelWon = Math.floor(Math.random() * 15) + 5;
            finalGameData = {
                ...finalGameData,
                resources: { ...finalGameData.resources, credits: finalGameData.resources.credits + creditsWon },
                ship: { 
                    ...finalGameData.ship, 
                    hull: { ...finalGameData.ship.hull, current: playerShip?.health ?? 0 }, 
                    shields: { ...finalGameData.ship.shields, current: playerShip?.shields ?? 0 },
                    fuel: { ...finalGameData.ship.fuel, current: Math.min(finalGameData.ship.fuel.max, finalGameData.ship.fuel.current + fuelWon) }
                },
            };
            toast({
                title: "Enemy destroyed!",
                description: `You salvaged ${creditsWon} credits and ${fuelWon} fuel.`,
                variant: "default",
                duration: 5000,
            });
            setGameData(finalGameData);
            setTimeout(() => setGameState('starmap'), 3000);
        } else {
             finalGameData = {
                ...finalGameData,
                ship: { ...finalGameData.ship, hull: { ...finalGameData.ship.hull, current: 0 }, shields: { ...finalGameData.ship.shields, current: 0 } },
            };
            setGameData(finalGameData); // Update state before calling game over
            onGameOver({
                title: "You have been defeated!",
                description: "Your ship is lost to the void...",
            });
        }
    }, [gameData, battleTurns, playerShip, setGameData, setGameState, toast, onGameOver]);
    
     const startPlayerTurn = useCallback(() => {
        setTurn('player');
        setPlayerShip(p => {
            if (!p) return null;

            const baseRecharge = gameData.ship.shieldGenerator.rechargeRate;
            const engineer = gameData.ship.crew.find(c => c.role === 'Engineer');
            const engineerBonus = engineer?.bonusEffect.includes('+10%') ? 1.1 : 1.0;
            const totalRecharge = Math.floor(baseRecharge * engineerBonus);
            
            const newShields = Math.min(p.maxShields, p.shields + totalRecharge);
            const rechargedAmount = newShields - p.shields;

            let newLog = ["Your turn."];
            if (rechargedAmount > 0 && p.shields < p.maxShields) {
                newLog.push(`Shields recharged by ${rechargedAmount}.`);
            }

            setLog(prev => [...prev, ...newLog]);
            
            return {
                ...p,
                shields: newShields,
                actionPoints: { ...p.actionPoints, current: p.actionPoints.max }
            };
        });
    }, [gameData]);

    const handleAttack = useCallback((attacker: BattleShip, target: BattleShip): BattleShip => {
        let newLog: string[] = [];
        let newTarget = { ...target };

        // Accuracy check
        let accuracy = 0.95; // Base accuracy
        if (attacker.isPlayer) {
            const gunner = gameData.ship.crew.find(c => c.role === 'Gunner');
            if (gunner?.bonusEffect.includes('+5%')) {
                accuracy += 0.05;
            }
        }
        
        if (Math.random() > accuracy) {
            newLog.push(`${attacker.name}'s shot misses!`);
            setLog(prev => [...prev, ...newLog]);
            return target; // No change to target ship
        }

        const weapon = attacker.weapons[0];
        let damageDealt = weapon.damage;
        newLog.push(`${attacker.name} fires its ${weapon.name}!`);
        

        if (newTarget.shields > 0) {
            const shieldDamage = Math.min(newTarget.shields, damageDealt);
            newTarget.shields -= shieldDamage;
            damageDealt -= shieldDamage;
            newLog.push(`${target.name}'s shields absorb ${shieldDamage.toFixed(0)} damage.`);
        }

        if (damageDealt > 0) {
            newTarget.health -= damageDealt;
            newLog.push(`${target.name} takes ${damageDealt.toFixed(0)} damage to the hull!`);
        }

        if (newTarget.health <= 0) {
            newTarget.health = 0;
            newLog.push(`${target.name} has been destroyed!`);
            handleBattleEnd(!target.isPlayer);
        }

        setLog(prev => [...prev, ...newLog]);
        return newTarget;
    }, [toast, setGameState, gameData, handleBattleEnd]);


    // Initialize battle state
    useEffect(() => {
        if (!gameData.player) return;

        let finalThrust = gameData.ship.engine.thrust;
        const pilot = gameData.ship.crew.find(c => c.role === 'Pilot');
        if (pilot?.bonusEffect.includes('+1')) {
            finalThrust += 1;
        }

        setPlayerShip({
            id: 'player-ship',
            name: gameData.player.shipName,
            image: "https://placehold.co/128x128.png",
            health: gameData.ship.hull.current,
            maxHealth: gameData.ship.hull.max,
            shields: gameData.ship.shields.current,
            maxShields: gameData.ship.shields.max,
            weapons: gameData.ship.weapons.map(w => ({ name: w.name, damage: w.damage, range: w.range })),
            position: { x: 3, y: 6 },
            isPlayer: true,
            movementRange: finalThrust,
            actionPoints: { current: 2, max: 2 },
        });

        setEnemyShip({
            id: 'enemy-ship-1',
            name: 'Pirate Marauder',
            image: "https://placehold.co/128x128.png",
            health: 80,
            maxHealth: 80,
            shields: 40,
            maxShields: 40,
            weapons: [{ name: 'Pulse Gun', damage: 10, range: 4 }],
            position: { x: 10, y: 6 },
            isPlayer: false,
            movementRange: 3,
            actionPoints: { current: 2, max: 2 },
        });
        setLog(["Battle started!"]);
        startPlayerTurn();
    }, [gameData, startPlayerTurn]);

    // Enemy turn logic
    useEffect(() => {
        if (turn === 'enemy' && enemyShip && playerShip && enemyShip.health > 0 && playerShip.health > 0) {
            const enemyTurn = async () => {
                await new Promise(res => setTimeout(res, 500));
                let currentEnemyShip = {...enemyShip, actionPoints: {...enemyShip.actionPoints, current: enemyShip.actionPoints.max}};
                
                // 1. Move if not in range and has AP
                const dist = Math.abs(currentEnemyShip.position.x - playerShip.position.x) + Math.abs(currentEnemyShip.position.y - playerShip.position.y);
                if (dist > currentEnemyShip.weapons[0].range && currentEnemyShip.actionPoints.current > 0) {
                    let bestMove = currentEnemyShip.position;
                    let bestDist = dist;
                    const validMoves = getValidMoves(currentEnemyShip, playerShip);

                    for (const move of validMoves) {
                        const newDist = Math.abs(move.x - playerShip.position.x) + Math.abs(move.y - playerShip.position.y);
                        if (newDist < bestDist) {
                            bestDist = newDist;
                            bestMove = move;
                        }
                    }
                    if (bestMove !== currentEnemyShip.position) {
                        currentEnemyShip.position = bestMove;
                        currentEnemyShip.actionPoints.current -= 1;
                        setLog(prev => [...prev, `${currentEnemyShip.name} moves to ${bestMove.x},${bestMove.y}.`]);
                        await new Promise(res => setTimeout(res, 500));
                    }
                }
                setEnemyShip(currentEnemyShip);

                // 2. Attack if in range and has AP
                const newDist = Math.abs(currentEnemyShip.position.x - playerShip.position.x) + Math.abs(currentEnemyShip.position.y - playerShip.position.y);
                if (newDist <= currentEnemyShip.weapons[0].range && currentEnemyShip.actionPoints.current > 0) {
                    const updatedPlayerShip = handleAttack(currentEnemyShip, playerShip);
                    setPlayerShip(updatedPlayerShip);
                    currentEnemyShip.actionPoints.current -= 1;
                    setEnemyShip(currentEnemyShip);
                }
                
                await new Promise(res => setTimeout(res, 500));
                startPlayerTurn();
            };
            enemyTurn();
        }
    }, [turn, playerShip, enemyShip, getValidMoves, handleAttack, startPlayerTurn]);

    const handleSelectAttack = () => {
        if (turn !== 'player' || !playerShip || playerShip.actionPoints.current < 1) return;

        if (selectedAction === 'attack') {
            setSelectedAction(null);
            setAttackableTargets([]);
            setAttackRangeTiles([]);
            return;
        }

        const targetsInRange = ships
            .filter(s => !s.isPlayer && s.health > 0)
            .filter(s => {
                if (!playerShip) return false;
                const dist = Math.abs(playerShip.position.x - s.position.x) + Math.abs(playerShip.position.y - s.position.y);
                return dist <= playerShip.weapons[0].range;
            })
            .map(s => s.id);

        if (targetsInRange.length === 0) {
            toast({ title: "No targets in range!", description: "Move closer or end your turn." });
            return;
        }
        
        setSelectedAction('attack');
        setAttackableTargets(targetsInRange);
        setAttackRangeTiles(getAttackableTiles(playerShip));
        setHighlightedTiles([]); // Clear move highlights
    };
    
    const handleSelectMove = () => {
        if (turn !== 'player' || !playerShip || !enemyShip || playerShip.actionPoints.current < 1) return;

        if (selectedAction === 'move') {
            setSelectedAction(null);
            setHighlightedTiles([]);
            return;
        }

        setSelectedAction('move');
        setAttackableTargets([]);
        setAttackRangeTiles([]);
        setHighlightedTiles(getValidMoves(playerShip, enemyShip));
    };

    const handleGridTileClick = (x: number, y: number) => {
        if (selectedAction !== 'move' || !playerShip) return;
        if (highlightedTiles.some(tile => tile.x === x && tile.y === y)) {
            setPlayerShip(p => p ? { ...p, position: { x, y }, actionPoints: { ...p.actionPoints, current: p.actionPoints.current - 1 }} : null);
            setLog(prev => [...prev, `Moved to ${x},${y}.`]);
        }
        setHighlightedTiles([]);
        setSelectedAction(null);
    };

    const handleShipClick = (targetShip: BattleShip) => {
        if (selectedAction === 'attack' && playerShip && enemyShip && attackableTargets.includes(targetShip.id)) {
            const updatedEnemyShip = handleAttack(playerShip, enemyShip);
            setEnemyShip(updatedEnemyShip);
            setPlayerShip(p => p ? { ...p, actionPoints: { ...p.actionPoints, current: p.actionPoints.current - 1 } } : null);
            
            setSelectedAction(null);
            setAttackableTargets([]);
            setAttackRangeTiles([]);
        }
    };

    const endPlayerTurn = () => {
        if (turn !== 'player') return;
        setTurn('enemy');
        setBattleTurns(t => t + 1);
        setSelectedAction(null);
        setHighlightedTiles([]);
        setAttackableTargets([]);
        setAttackRangeTiles([]);
        setLog(prev => [...prev, "Ending turn."]);
    };
    
    const handleRetreat = () => {
        const finalGameData = { ...gameData, gameTurn: gameData.gameTurn + battleTurns };
        setGameData(finalGameData);
        toast({
            title: "You retreated from battle.",
            variant: "default"
        });
        setGameState('starmap');
    }

    const renderGrid = () => {
        const cells = [];
        for (let y = 1; y <= GRID_SIZE; y++) {
            for (let x = 1; x <= GRID_SIZE; x++) {
                const isMoveTile = highlightedTiles.some(t => t.x === x && t.y === y);
                const isAttackTile = attackRangeTiles.some(t => t.x === x && t.y === y);

                cells.push(
                    <div 
                        key={`${x}-${y}`}
                        className={cn("border border-white/10 transition-colors duration-200", {
                            "bg-blue-500/20 cursor-pointer hover:bg-blue-500/40 z-10": isMoveTile,
                            "bg-red-500/10": isAttackTile && !isMoveTile
                        })}
                        style={{ gridColumnStart: x, gridRowStart: y }}
                        onClick={() => handleGridTileClick(x, y)}
                    />
                );
            }
        }
        return cells;
    };
    
    if (!playerShip || !enemyShip) return null; // Or a loading state

    return (
        <div className="h-full w-full bg-background flex flex-col relative overflow-hidden battle-grid-bg">
            <style jsx>{`
                .battle-grid-bg {
                    background-color: #0A0A0A;
                    background-image: radial-gradient(circle at center, rgba(255,152,0,0.1), transparent 40%),
                        linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
                    background-size: 100% 100%, clamp(3rem, 7vmin, 5rem) clamp(3rem, 7vmin, 5rem), clamp(3rem, 7vmin, 5rem) clamp(3rem, 7vmin, 5rem);
                }
            `}</style>
            
            <BattleHeader onRetreat={handleRetreat} />

            <main className="flex-grow w-full h-full p-20 relative">
                <div className="w-full h-full grid" style={{gridTemplateColumns: `repeat(${GRID_SIZE}, 1fr)`, gridTemplateRows: `repeat(${GRID_SIZE}, 1fr)`}}>
                    {renderGrid()}

                    {ships.map(ship => {
                        const isAttackable = selectedAction === 'attack' && attackableTargets.includes(ship.id);
                        return (
                            <motion.div
                                key={ship.id}
                                className="flex items-center justify-center relative z-10"
                                style={{
                                    gridColumnStart: ship.position.x,
                                    gridRowStart: ship.position.y
                                }}
                                onMouseEnter={() => setHoveredShip(ship)}
                                onMouseLeave={() => setHoveredShip(null)}
                                onClick={() => handleShipClick(ship)}
                                layout
                                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                            >
                                <Image 
                                    src={ship.image}
                                    alt={ship.name}
                                    width={64}
                                    height={64}
                                    className={cn("transform transition-transform duration-300 hover:scale-110", {
                                        "hue-rotate-[200deg] brightness-125": ship.isPlayer,
                                        "drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]": ship.isPlayer && !isAttackable,
                                        "drop-shadow-[0_0_8px_rgba(239,68,68,0.8)]": !ship.isPlayer && !isAttackable,
                                        "drop-shadow-[0_0_12px_rgba(239,68,68,1)] ring-2 ring-red-500/80 cursor-crosshair": isAttackable,
                                    })}
                                    data-ai-hint={ship.isPlayer ? "player spaceship" : "enemy spaceship"}
                                 />
                            </motion.div>
                        )
                    })}

                    <AnimatePresence>
                        <ShipStatusOverlay ship={hoveredShip} />
                    </AnimatePresence>
                </div>
            </main>

            <div className="absolute left-4 bottom-24 text-white text-xs font-mono h-32 w-80 p-2 bg-black/40 rounded overflow-y-auto z-20">
                {log.slice(-10).map((l, i) => <p key={i} className="animate-in fade-in slide-in-from-bottom-2 duration-300">{l}</p>)}
            </div>

            <BattleControls onAttack={handleSelectAttack} onEndTurn={endPlayerTurn} onMove={handleSelectMove} isPlayerTurn={turn === 'player'} playerAp={playerShip?.actionPoints.current ?? 0} selectedAction={selectedAction} />
        </div>
    );
}
