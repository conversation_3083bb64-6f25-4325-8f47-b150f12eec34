"use client";

import type { GameData, GameState, ShipComponent, Engine, ShieldGenerator, WeaponSystem, CrewMember, Artifact } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';
import { Label } from '../ui/label';
import type { Dispatch, SetStateAction } from 'react';
import { ArrowLeft, Rocket, Users, Package, Zap, ChevronsRight, Shield, Heart, Fuel, Cog, Crosshair, Sparkles } from 'lucide-react';

type ShipManagementScreenProps = {
  gameData: GameData;
  setGameData: Dispatch<SetStateAction<GameData>>;
  setGameState: (state: GameState) => void;
};

const getPercentage = (current: number, max: number) => max > 0 ? (current / max) * 100 : 0;

const Stat = ({ label, value, icon }: { label: string, value: React.ReactNode, icon?: React.ReactNode }) => (
    <div className="flex justify-between items-center text-sm">
        <div className="flex items-center gap-2 text-muted-foreground">
            {icon}
            <span>{label}</span>
        </div>
        <span className="font-mono font-medium">{value}</span>
    </div>
);

const ComponentDetailCard = ({ component }: { component: ShipComponent }) => {
    let stats = null;

    if (component.type === 'engine') {
        const engine = component as Engine;
        stats = <Stat label="Thrust" value={engine.thrust} />;
    } else if (component.type === 'shield') {
        const shield = component as ShieldGenerator;
        stats = (
            <>
                <Stat label="Capacity" value={shield.capacity} />
                <Stat label="Recharge Rate" value={`${shield.rechargeRate}/turn`} />
            </>
        );
    } else if (component.type === 'weapon') {
        const weapon = component as WeaponSystem;
        stats = (
            <>
                <Stat label="Damage" value={weapon.damage} />
                <Stat label="Range" value={weapon.range} />
            </>
        );
    } else if (component.type === 'artifact') {
        const artifact = component as Artifact;
        stats = (
             <p className="text-sm text-purple-400 italic">{artifact.effectDescription}</p>
        )
    }

    return (
        <Card className="bg-muted/30">
            <CardHeader className="pb-2">
                <CardTitle className="text-lg font-headline flex items-center gap-2">
                    {component.type === 'artifact' && <Sparkles className="text-purple-400" />}
                    {component.name}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 pt-4">
                <p className="text-sm text-muted-foreground">{component.description}</p>
                <Separator/>
                <div className="space-y-1">
                    {stats}
                    <Stat label="Power Draw" value={component.powerDraw || 0} />
                    <Stat label="Mass" value={component.mass} />
                </div>
            </CardContent>
        </Card>
    );
};


export default function ShipManagementScreen({ gameData, setGameData, setGameState }: ShipManagementScreenProps) {
  const { player, ship } = gameData;
  
  const handleEquipWeapon = (weaponToEquip: WeaponSystem) => {
    setGameData(prev => {
        const newShipData = { ...prev.ship };

        // For simplicity, we assume one weapon slot. This can be expanded later.
        const currentWeapon = newShipData.weapons[0];

        // Filter out the weapon to equip from cargo
        const newCargo = newShipData.cargo.filter(item => item.id !== weaponToEquip.id);

        // Add the previously equipped weapon back to cargo
        if (currentWeapon) {
            newCargo.push(currentWeapon);
        }

        // Equip the new weapon
        newShipData.weapons = [weaponToEquip];
        newShipData.cargo = newCargo;

        return { ...prev, ship: newShipData };
    });
  };

  return (
    <div className="h-full w-full bg-background p-8 flex items-center justify-center">
      <Card className="w-full max-w-5xl h-[90vh] flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-4xl font-headline text-primary">The {player?.shipName}</CardTitle>
            <CardDescription>Manage your ship, crew, and cargo.</CardDescription>
          </div>
          <Button variant="outline" onClick={() => setGameState('starmap')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Return to Starmap
          </Button>
        </CardHeader>
        <CardContent className="h-full py-4 flex-grow">
            <Tabs defaultValue="ship" className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="ship"><Rocket className="mr-2"/>Ship</TabsTrigger>
                    <TabsTrigger value="cargo"><Package className="mr-2"/>Cargo</TabsTrigger>
                    <TabsTrigger value="crew"><Users className="mr-2"/>Crew</TabsTrigger>
                </TabsList>

                <TabsContent value="ship" className="flex-grow p-4 bg-muted/20 rounded-b-lg mt-2 overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Column 1: Core Stats */}
                        <div className="col-span-1 space-y-4">
                            <h2 className="text-2xl font-headline mb-2">Core Stats</h2>
                             <Card className="bg-muted/30">
                                <CardContent className="p-4 space-y-3">
                                    <div className="space-y-1">
                                        <Label className="text-sm flex items-center justify-between"><span><Heart className="inline mr-2 h-4 w-4 text-red-500" />Hull</span><span>{ship.hull.current} / {ship.hull.max}</span></Label>
                                        <Progress value={getPercentage(ship.hull.current, ship.hull.max)} />
                                    </div>
                                    <div className="space-y-1">
                                        <Label className="text-sm flex items-center justify-between"><span><Shield className="inline mr-2 h-4 w-4 text-blue-500" />Shields</span><span>{ship.shields.current} / {ship.shields.max}</span></Label>
                                        <Progress value={getPercentage(ship.shields.current, ship.shields.max)} />
                                    </div>
                                    <div className="space-y-1">
                                        <Label className="text-sm flex items-center justify-between"><span><Fuel className="inline mr-2 h-4 w-4 text-yellow-500" />Fuel</span><span>{ship.fuel.current} / {ship.fuel.max}</span></Label>
                                        <Progress value={getPercentage(ship.fuel.current, ship.fuel.max)} />
                                    </div>
                                     <div className="space-y-1">
                                        <Label className="text-sm flex items-center justify-between"><span><Zap className="inline mr-2 h-4 w-4 text-green-500" />Power</span><span>{ship.power.current} / {ship.power.max}</span></Label>
                                        <Progress value={getPercentage(ship.power.current, ship.power.max)} />
                                    </div>
                                </CardContent>
                             </Card>
                        </div>

                        {/* Column 2: Equipped Systems */}
                        <div className="col-span-2 space-y-4">
                            <h2 className="text-2xl font-headline mb-2">Equipped Systems</h2>
                             <div className="space-y-4">
                                <div>
                                    <h3 className="font-semibold mb-2 flex items-center gap-2"><ChevronsRight/>Engine</h3>
                                    <ComponentDetailCard component={ship.engine} />
                                </div>
                                <Separator />
                                 <div>
                                    <h3 className="font-semibold mb-2 flex items-center gap-2"><Shield/>Shield Generator</h3>
                                    <ComponentDetailCard component={ship.shieldGenerator} />
                                </div>
                                <Separator/>
                                <div>
                                    <h3 className="font-semibold mb-2 flex items-center gap-2"><Zap/>Weapon Systems</h3>
                                    <div className="space-y-2">
                                        {ship.weapons.map(w => <ComponentDetailCard key={w.id} component={w} />)}
                                    </div>
                                </div>
                             </div>
                        </div>
                    </div>
                </TabsContent>

                <TabsContent value="cargo" className="flex-grow p-4 bg-muted/20 rounded-b-lg mt-2 overflow-y-auto">
                    <h2 className="text-2xl font-headline mb-4">Cargo Hold</h2>
                     {ship.cargo.length === 0 ? (
                        <p className="text-center text-muted-foreground mt-16">Cargo hold is empty.</p>
                     ) : (
                        <div className="space-y-4">
                           {ship.cargo.map((item) => (
                               <Card key={item.id} className="flex items-center justify-between p-4 bg-muted/30">
                                   <div>
                                       <h4 className="font-bold">{item.name}</h4>
                                       <p className="text-sm text-muted-foreground">{item.description}</p>
                                   </div>
                                    {item.type === 'weapon' ? (
                                        <Button variant="outline" size="sm" onClick={() => handleEquipWeapon(item as WeaponSystem)}>Equip</Button>
                                    ) : (
                                        <Button variant="outline" size="sm" disabled>Cannot Equip</Button>
                                    )}
                               </Card>
                           ))}
                        </div>
                     )}
                </TabsContent>

                <TabsContent value="crew" className="flex-grow p-4 bg-muted/20 rounded-b-lg mt-2 overflow-y-auto">
                    <h2 className="text-2xl font-headline mb-4">Crew Roster</h2>
                    {ship.crew.length === 0 ? (
                        <p className="text-center text-muted-foreground mt-16">No crew assigned. Your ship is running on automation.</p>
                     ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                           {ship.crew.map((member) => {
                                const Icon = member.role === 'Pilot' ? Users : member.role === 'Engineer' ? Cog : Crosshair;
                                return (
                                    <Card key={member.id} className="flex flex-col bg-muted/30">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-3">
                                                <Icon className="h-6 w-6 text-primary" />
                                                <div>
                                                    <p className="text-xl font-headline">{member.name}</p>
                                                    <p className="text-sm font-normal text-muted-foreground">{member.role} - Skill: {member.skillLevel}</p>
                                                </div>
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="flex-grow">
                                            <p className="text-sm text-muted-foreground italic mb-3">"{member.description}"</p>
                                            <p className="text-sm"><strong className="text-foreground">Ship Bonus:</strong> {member.bonusEffect}</p>
                                        </CardContent>
                                    </Card>
                                );
                           })}
                        </div>
                     )}
                </TabsContent>
            </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
