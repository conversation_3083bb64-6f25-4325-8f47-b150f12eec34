"use client";

import { useState } from 'react';
import Image from 'next/image';
import type { GameData, GameState, ShipComponent, Artifact, Planet } from '@/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { useToast } from '@/hooks/use-toast';
import type { Dispatch, SetStateAction } from 'react';
import { ArrowLeft, Box, Coins, Fuel as FuelIcon, Skull, Wrench, HandCoins } from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../ui/alert-dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

type PlanetScreenProps = {
  gameData: GameData;
  setGameData: Dispatch<SetStateAction<GameData>>;
  setGameState: (state: GameState) => void;
};

export default function PlanetScreen({ gameData, setGameData, setGameState }: PlanetScreenProps) {
  const { toast } = useToast();
  const [isRefuelDialogOpen, setIsRefuelDialogOpen] = useState(false);
  const [fuelToBuy, setFuelToBuy] = useState(0);

  const currentSystem = gameData.starmap.systems.find(p => p.id === gameData.currentSystemId);
  const currentPlanet = currentSystem?.planets.find(p => p.id === gameData.currentPlanetId);
  
  if (!currentPlanet || !currentSystem) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <p>Error: Planet not found. Returning to starmap...</p>
        <Button onClick={() => setGameState('starmap')}>Go Back</Button>
      </div>
    );
  }

  const handleExplore = () => {
    // Increment turn counter for the action
    let updatedGameData = { ...gameData, gameTurn: gameData.gameTurn + 1 };

    if (currentPlanet.hasBeenExplored) {
      toast({ title: "Area Already Scanned", description: "You've already explored this area. There's nothing new to find." });
      setGameData(updatedGameData);
      return;
    }

    // Mark planet as explored immediately
     const newSystems = updatedGameData.starmap.systems.map(s => {
        if (s.id !== currentSystem.id) return s;
        return {
            ...s,
            planets: s.planets.map(p => 
                p.id === currentPlanet.id ? { ...p, hasBeenExplored: true } : p
            )
        };
    });
    updatedGameData = { ...updatedGameData, starmap: { ...updatedGameData.starmap, systems: newSystems } };


    const discovery = currentPlanet.discovery;
    toast({
        title: `Discovery: ${discovery.type.charAt(0).toUpperCase() + discovery.type.slice(1)}`,
        description: discovery.description
    });

    switch (discovery.type) {
        case 'credits': {
            const creditsFound = discovery.value as number;
            updatedGameData = {
                ...updatedGameData,
                resources: {
                    ...updatedGameData.resources,
                    credits: updatedGameData.resources.credits + creditsFound
                }
            };
            break;
        }
        case 'fuel': {
            const fuelFound = discovery.value as number;
            updatedGameData = {
                ...updatedGameData,
                ship: {
                    ...updatedGameData.ship,
                    fuel: {
                        ...updatedGameData.ship.fuel,
                        current: Math.min(updatedGameData.ship.fuel.max, updatedGameData.ship.fuel.current + fuelFound)
                    }
                }
            };
            break;
        }
        case 'artifact': {
            const artifactFound = discovery.value as Artifact;
            updatedGameData = {
                ...updatedGameData,
                ship: {
                    ...updatedGameData.ship,
                    cargo: [...updatedGameData.ship.cargo, artifactFound]
                }
            };
            break;
        }
        case 'enemy':
            setGameData(updatedGameData); // save turn increment before battle
            setGameState('battle');
            return; // exit early to prevent setting game data again
        case 'nothing':
        case 'outpost':
            // No immediate data change, just toast message is shown
            break;
    }

    setGameData(updatedGameData);
  };
  
  const handleRefuel = () => {
    const fuelNeeded = gameData.ship.fuel.max - gameData.ship.fuel.current;
    if (fuelNeeded === 0) {
        toast({ title: "Fuel tanks are already full." });
        return;
    }
    setFuelToBuy(fuelNeeded);
    setIsRefuelDialogOpen(true);
  }

  const confirmRefuel = () => {
    const cost = fuelToBuy * 2; // 2 credits per unit of fuel
    if (gameData.resources.credits < cost) {
      toast({ title: "Insufficient Credits", description: `You need ${cost} credits to buy ${fuelToBuy} fuel.`, variant: "destructive" });
      return;
    }
    if (fuelToBuy <= 0) {
        toast({ title: "Invalid Amount", description: "You must purchase a positive amount of fuel.", variant: "destructive"});
        return;
    }

    setGameData(prev => ({
        ...prev,
        resources: {
            ...prev.resources,
            credits: prev.resources.credits - cost
        },
        ship: {
            ...prev.ship,
            fuel: {
                ...prev.ship.fuel,
                current: Math.min(prev.ship.fuel.max, prev.ship.fuel.current + fuelToBuy)
            }
        }
    }));
    
    toast({ title: "Refuel Successful", description: `Purchased ${fuelToBuy} fuel for ${cost} credits.`});
    setIsRefuelDialogOpen(false);
  }

  const handleLeave = () => {
    setGameState('system-view');
  };

  const renderDiscoveryInfo = () => {
    if (currentPlanet.discovery.type === 'outpost') {
        return (
            <div className="flex flex-col gap-2">
                 <p className="text-center text-muted-foreground p-4 bg-muted/50 rounded-lg">{currentPlanet.discovery.description}</p>
                 <Button size="lg" className="w-full" onClick={handleRefuel}>
                    <FuelIcon className="mr-2" />
                    Refuel Ship (2c/unit)
                </Button>
                <Button size="lg" className="w-full" disabled>
                    <HandCoins className="mr-2" />
                    Trade at MerchGenie (Coming Soon)
                </Button>
            </div>
        )
    }
    
    if (currentPlanet.hasBeenExplored) {
        return <p className="text-center text-muted-foreground">You have already thoroughly explored this planet.</p>
    }

    const { discovery } = currentPlanet;
    let icon = <Box className="h-6 w-6 text-primary" />;
    let text = "Unknown Signal";
    switch (discovery.type) {
        case 'credits':
            icon = <Coins className="h-6 w-6 text-yellow-400" />;
            text = `Potential financial gain detected.`;
            break;
        case 'fuel':
            icon = <FuelIcon className="h-6 w-6 text-green-400" />;
            text = `Refined fuel signature detected.`;
            break;
        case 'artifact':
            icon = <Box className="h-6 w-6 text-purple-400" />;
            text = "Unusual artifact signature detected.";
            break;
        case 'enemy':
            icon = <Skull className="h-6 w-6 text-red-500" />;
            text = "Warning: Hostile vessel signatures detected.";
            break;
        case 'nothing':
             icon = <div className="h-6 w-6" />;
             text = "Sensor readings are unclear.";
             break;
    }
    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-center gap-4 p-4 bg-muted/50 rounded-lg">
                {icon}
                <p className="text-muted-foreground">{text}</p>
            </div>
            <Button size="lg" className="w-full" onClick={handleExplore}>
                <Wrench className="mr-2" />
                Explore Planet
            </Button>
        </div>
    );
  }

  return (
    <>
    <div className="h-full w-full bg-cover bg-center p-8 flex items-center justify-center" style={{backgroundImage: `url('${currentPlanet.image}')`}} data-ai-hint="alien planet landscape">
      <div className="absolute inset-0 bg-black/50" />
      <Card className="w-full max-w-md z-10 bg-card/80 backdrop-blur-sm">
        <CardHeader>
          <Button variant="ghost" size="icon" className="absolute top-3 left-3" onClick={handleLeave}>
              <ArrowLeft />
          </Button>
          <CardTitle className="text-4xl font-headline text-primary text-center pt-8">{currentPlanet.name}</CardTitle>
          <CardDescription className="text-center">{currentPlanet.description}</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          {renderDiscoveryInfo()}
        </CardContent>
        <CardFooter>
            <Button variant="secondary" className="w-full" onClick={handleLeave}>
                Leave Orbit
            </Button>
        </CardFooter>
      </Card>
    </div>

    <AlertDialog open={isRefuelDialogOpen} onOpenChange={setIsRefuelDialogOpen}>
        <AlertDialogContent>
            <AlertDialogHeader>
            <AlertDialogTitle>Refuel Ship</AlertDialogTitle>
            <AlertDialogDescription>
                Your tanks can hold {gameData.ship.fuel.max} units. You currently have {gameData.ship.fuel.current}.
                Fuel costs 2 credits per unit.
            </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="fuel-amount" className="text-right">Amount</Label>
                    <Input
                        id="fuel-amount"
                        type="number"
                        value={fuelToBuy}
                        onChange={(e) => {
                            const val = parseInt(e.target.value, 10);
                            const maxBuy = gameData.ship.fuel.max - gameData.ship.fuel.current;
                            setFuelToBuy(Math.max(0, Math.min(val, maxBuy)));
                        }}
                        className="col-span-3"
                    />
                </div>
                 <div className="grid grid-cols-4 items-center gap-4">
                    <Label className="text-right">Cost</Label>
                    <p className="col-span-3 font-mono">{fuelToBuy * 2} Credits</p>
                 </div>
            </div>
            <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmRefuel}>Confirm Purchase</AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
    </>
  );
}
