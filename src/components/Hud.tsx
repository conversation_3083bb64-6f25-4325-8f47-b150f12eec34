import type { GameData } from '@/types';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Heart, Shield, Fuel, Coins, Map, Ship as ShipIcon, Pause, Play, LogOut, History } from 'lucide-react';
import { Progress } from './ui/progress';
import { SDK } from '@/lib/goama-sdk';

type HudProps = {
  gameData: GameData;
  setGameState: (state: any) => void;
  isPaused: boolean;
  togglePause: () => void;
  handleQuit: () => void;
};

export default function Hud({ gameData, setGameState, isPaused, togglePause, handleQuit }: HudProps) {
  const { player, ship, resources, gameTurn } = gameData;
  
  const handlePause = () => {
    SDK.gamePaused();
    togglePause();
  };

  const handleResume = () => {
    SDK.gameResumed();
    togglePause();
  };
  
  const getPercentage = (current: number, max: number) => {
    if (max === 0) return 0;
    return (current / max) * 100;
  }

  return (
    <div className="absolute inset-0 p-4 pointer-events-none grid grid-rows-[auto_1fr_auto] grid-cols-[auto_1fr_auto] gap-4">
      {/* Top-left: Player Info */}
      <Card className="col-start-1 row-start-1 pointer-events-auto bg-card/80 backdrop-blur-sm">
        <CardHeader className="p-3">
          <CardTitle className="text-lg font-headline">{player?.name}</CardTitle>
          <p className="text-sm text-muted-foreground">{player?.race.name} Captain of the {player?.shipName}</p>
           <div className="flex items-center gap-2 text-sm text-muted-foreground pt-1">
                <History className="h-4 w-4" />
                <span>Turn: {gameTurn}</span>
            </div>
        </CardHeader>
      </Card>

      {/* Top-right: Ship Status */}
      <Card className="col-start-3 row-start-1 pointer-events-auto bg-card/80 backdrop-blur-sm">
        <CardContent className="p-3 space-y-2">
            <div className="flex items-center gap-2" title="Hull">
                <Heart className="h-4 w-4 text-red-500" />
                <Progress value={getPercentage(ship.hull.current, ship.hull.max)} className="w-32 h-2" />
                <span className="text-xs font-mono">{ship.hull.current}/{ship.hull.max}</span>
            </div>
            <div className="flex items-center gap-2" title="Shields">
                <Shield className="h-4 w-4 text-blue-500" />
                <Progress value={getPercentage(ship.shields.current, ship.shields.max)} className="w-32 h-2" />
                <span className="text-xs font-mono">{ship.shields.current}/{ship.shields.max}</span>
            </div>
             <div className="flex items-center gap-2" title="Fuel">
                <Fuel className="h-4 w-4 text-yellow-500" />
                <Progress value={getPercentage(ship.fuel.current, ship.fuel.max)} className="w-32 h-2" />
                <span className="text-xs font-mono">{ship.fuel.current}/{ship.fuel.max}</span>
            </div>
        </CardContent>
      </Card>

      {/* Bottom-left: Resources */}
       <Card className="col-start-1 row-start-3 pointer-events-auto bg-card/80 backdrop-blur-sm">
        <CardContent className="p-3 flex items-center gap-2">
            <Coins className="h-5 w-5 text-primary" />
            <span className="text-lg font-mono font-bold">{resources.credits.toLocaleString()}</span>
            <span className="text-muted-foreground text-sm">Credits</span>
        </CardContent>
      </Card>
      
      {/* Bottom-right: Actions */}
      <div className="col-start-3 row-start-3 pointer-events-auto flex gap-2 items-center">
        <Button size="icon" variant="secondary" onClick={() => setGameState('starmap')} title="Starmap"><Map/></Button>
        <Button size="icon" variant="secondary" onClick={() => setGameState('ship-management')} title="Ship Management"><ShipIcon/></Button>
        {isPaused ? 
            <Button size="icon" variant="secondary" onClick={handleResume} title="Resume"><Play/></Button> :
            <Button size="icon" variant="secondary" onClick={handlePause} title="Pause"><Pause/></Button>
        }
        <Button size="icon" variant="destructive" onClick={handleQuit} title="Quit Game (Saves progress)">
            <LogOut/>
        </Button>
      </div>

    </div>
  );
}
