'use client';

import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Login() {
  const router = useRouter();

  useEffect(() => {
    // Ensure React and ReactDOM are available globally from the script tags
    if (!window.React || !window.ReactDOM || !window.Bedrock) {
      console.error('Error: Required Bedrock libraries failed to load.');
      // Optionally, display a user-friendly message
      const container = document.getElementById('bedrock-login-widget');
      if (container) {
        container.innerText = 'Error loading authentication widget. Please check your internet connection.';
      }
      return;
    }

    const bedrockConfig = {
      baseUrl: 'https://api.bedrockpassport.com',
      authCallbackUrl: window.location.origin,
      tenantId: 'orange-abc123', // Your assigned tenant ID - you can request one at https://vibecodinglist.com/orange-id-integration
      subscriptionKey: 'your_API_Key', // Your assigned API Key - you can request one at https://vibecodinglist.com/orange-id-integration
    };

    const container = document.getElementById('bedrock-login-widget');
    if (!container) {
      console.error('Bedrock login widget container not found.');
      return;
    }

    const root = ReactDOM.createRoot(container);
    const params = new URLSearchParams(window.location.search);
    const token = params.get('token');
    const refreshToken = params.get('refreshToken');

    if (token && refreshToken) {
      // We're handling a callback
      function AuthCallbackProcessor() {
        const { loginCallback } = window.Bedrock.useBedrockPassport();
        const [message, setMessage] = React.useState('Processing authentication...');

        React.useEffect(() => {
          async function processLogin() {
            try {
              setMessage('Verifying tokens...');
              const success = await loginCallback(token, refreshToken);

              if (success) {
                setMessage('Login successful! Redirecting...');
                window.history.replaceState({}, document.title, window.location.pathname);
                router.push('/dashboard.html'); // Redirect to your dashboard page
              } else {
                setMessage('Authentication failed. Please try again.');
              }
            } catch (error) {
              console.error('Login error:', error);
              setMessage('An error occurred during login.');
            }
          }
          processLogin();
        }, [loginCallback]);

        return React.createElement('div', null, message);
      }

      root.render(
        React.createElement(
          window.Bedrock.BedrockPassportProvider,
          bedrockConfig,
          React.createElement(AuthCallbackProcessor)
        )
      );
    } else {
      // Normal login flow
      root.render(
        React.createElement(
          window.Bedrock.BedrockPassportProvider,
          bedrockConfig,
          React.createElement(window.Bedrock.LoginPanel, {
            title: 'Sign in to',
            logo: 'https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg',
            logoAlt: 'Orange Web3',
            walletButtonText: 'Connect Wallet',
            showConnectWallet: false,
            separatorText: 'OR',
            features: {
              enableWalletConnect: false,
              enableAppleLogin: true,
              enableGoogleLogin: true,
              enableEmailLogin: false,
            },
            titleClass: 'text-xl font-bold',
            logoClass: 'ml-2 md:h-8 h-6',
            panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
            buttonClass: 'hover:border-orange-500',
            separatorTextClass: 'bg-orange-900 text-gray-500',
            separatorClass: 'bg-orange-900',
            linkRowClass: 'justify-center',
            headerClass: 'justify-center',
          })
        )
      );
    }
  }, [router]);

  return (
    <div id="bedrock-login-widget"></div>
  );
}
