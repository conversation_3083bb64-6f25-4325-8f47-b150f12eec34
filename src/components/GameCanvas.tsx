"use client";

import { useRef, useEffect, useCallback } from 'react';
import type { StarSystem } from '@/types';

type GameCanvasProps = React.CanvasHTMLAttributes<HTMLCanvasElement> & {
    systems?: StarSystem[];
    onSystemClick?: (system: StarSystem) => void;
    shipPosition?: { x: number; y: number } | null;
    travelRange?: number;
};

const ShipIcon = ({ ctx, x, y, rotation }: { ctx: CanvasRenderingContext2D, x: number, y: number, rotation: number }) => {
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(rotation);
    ctx.beginPath();
    ctx.moveTo(0, -12); // Nose
    ctx.lineTo(8, 10);  // Right wing
    ctx.lineTo(0, 5); // Tail center
    ctx.lineTo(-8, 10); // Left wing
    ctx.closePath();
    ctx.fillStyle = 'hsl(var(--primary-foreground))';
    ctx.strokeStyle = 'hsl(var(--primary))';
    ctx.lineWidth = 2;
    ctx.shadowColor = 'hsl(var(--primary))';
    ctx.shadowBlur = 20;
    ctx.fill();
    ctx.stroke();
    ctx.restore();
};

const GameCanvas = ({ systems = [], onSystemClick, shipPosition, travelRange = 0, ...props }: GameCanvasProps) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const starsRef = useRef<{x: number, y: number, r: number, opacity: number}[]>([]);
    const scaledSystemsRef = useRef<(StarSystem & {scaledX: number, scaledY: number})[]>([]);
    const lastShipPos = useRef<{x: number, y: number} | null>(null);
    const scaleFactor = useRef({x: 1, y: 1});

    const draw = useCallback((ctx: CanvasRenderingContext2D, frameCount: number) => {
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        
        // Draw stars
        starsRef.current.forEach(star => {
            ctx.beginPath();
            ctx.arc(star.x, star.y, star.r, 0, 2 * Math.PI);
            ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
            ctx.fill();
        });
        
        // Example pulsing nebula
        const nebulaAlpha = Math.sin(frameCount * 0.005) * 0.08 + 0.12;
        const grd = ctx.createRadialGradient(ctx.canvas.width/2, ctx.canvas.height/2, 50, ctx.canvas.width/2, ctx.canvas.height/2, Math.max(ctx.canvas.width, ctx.canvas.height) / 2);
        grd.addColorStop(0, `hsla(36, 100%, 50%, ${nebulaAlpha})`);
        grd.addColorStop(1, `rgba(26, 35, 126, 0)`);
        ctx.fillStyle = grd;
        ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

        // Draw systems
        scaledSystemsRef.current.forEach(system => {
            ctx.beginPath();
            ctx.arc(system.scaledX, system.scaledY, system.size, 0, 2 * Math.PI);
            ctx.fillStyle = system.color;
            ctx.shadowColor = system.color;
            ctx.shadowBlur = 15;
            ctx.fill();

            // Draw system name
            ctx.fillStyle = 'white';
            ctx.font = '12px "Space Grotesk"';
            ctx.textAlign = 'center';
            ctx.shadowBlur = 0;
            ctx.fillText(system.name, system.scaledX, system.scaledY + system.size + 15);
        });
        ctx.shadowBlur = 0; // Reset shadow blur

        // Draw ship and travel range
        if (shipPosition) {
            const shipX = shipPosition.x * scaleFactor.current.x;
            const shipY = shipPosition.y * scaleFactor.current.y;
            const scaledRange = travelRange * scaleFactor.current.x;

            // Draw travel range
            if (scaledRange > 0) {
              ctx.beginPath();
              ctx.arc(shipX, shipY, scaledRange, 0, 2 * Math.PI);
              ctx.strokeStyle = 'hsla(36, 100%, 50%, 0.3)';
              ctx.lineWidth = 1;
              ctx.stroke();
              ctx.fillStyle = 'hsla(36, 100%, 50%, 0.05)';
              ctx.fill();
            }
            
            // Calculate ship rotation
            let rotation = Math.PI; // Default to facing down
            if (lastShipPos.current && (lastShipPos.current.x !== shipX || lastShipPos.current.y !== shipY)) {
                rotation = Math.atan2(shipY - lastShipPos.current.y, shipX - lastShipPos.current.x) + Math.PI / 2;
            }
            lastShipPos.current = { x: shipX, y: shipY };

            // Draw ship
            ShipIcon({ ctx, x: shipX, y: shipY, rotation });
        }
    }, [travelRange, shipPosition]);
    
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const context = canvas.getContext('2d');
        if (!context) return;
        
        const resizeCanvas = () => {
            const { devicePixelRatio: ratio = 1 } = window;
            const container = canvas.parentElement;
            if(!container) return;

            const { width, height } = container.getBoundingClientRect();
            canvas.width = width * ratio;
            canvas.height = height * ratio;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            context.scale(ratio, ratio);

            const originalWidth = 1920;
            const originalHeight = 1080;
            scaleFactor.current = { x: width / originalWidth, y: height / originalHeight };

            // Re-initialize stars on resize
            starsRef.current = [];
            for (let i = 0; i < 200; i++) {
                starsRef.current.push({
                    x: Math.random() * width,
                    y: Math.random() * height,
                    r: Math.random() * 1.5,
                    opacity: Math.random() * 0.5 + 0.5
                });
            }

            // Scale systems to current canvas size
            scaledSystemsRef.current = systems.map(s => ({
                ...s,
                scaledX: s.x * scaleFactor.current.x,
                scaledY: s.y * scaleFactor.current.y,
            }));
        };
        
        resizeCanvas();
        
        let frameCount = 0;
        let animationFrameId: number;

        const render = () => {
            frameCount++;
            draw(context, frameCount);
            animationFrameId = window.requestAnimationFrame(render);
        };
        
        render();

        const handleClick = (event: MouseEvent) => {
            if (!onSystemClick) return;
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // Find clicked system
            const clickedSystem = scaledSystemsRef.current.find(system => {
                const distance = Math.sqrt(Math.pow(x - system.scaledX, 2) + Math.pow(y - system.scaledY, 2));
                return distance < system.size + 5; // Add a little buffer
            });

            if (clickedSystem) {
                // Find original system data to pass back
                const originalSystem = systems.find(s => s.id === clickedSystem.id);
                if (originalSystem) {
                    onSystemClick(originalSystem);
                }
            }
        };

        canvas.addEventListener('click', handleClick);
        window.addEventListener('resize', resizeCanvas);
        
        return () => {
            window.cancelAnimationFrame(animationFrameId);
            window.removeEventListener('resize', resizeCanvas);
            canvas.removeEventListener('click', handleClick);
        };
    }, [draw, onSystemClick, systems]);

    return <canvas ref={canvasRef} {...props} className="absolute top-0 left-0 w-full h-full cursor-pointer" />;
};

export default GameCanvas;
