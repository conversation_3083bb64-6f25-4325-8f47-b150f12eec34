# Starship Voyager

This is a Next.js turn-based space exploration and combat game, created in Firebase Studio.

## Getting Started: Running Locally

To run the application on your local machine for development, follow these steps.

1.  **Install Dependencies:** The necessary packages are already listed in `package.json`. They will be installed automatically when the environment starts. If you need to do it manually, run:
    ```bash
    npm install
    ```

2.  **Run the Development Server:** Start the Next.js development server. This will also start the Genkit AI development server.
    ```bash
    npm run dev
    ```

3.  **Open Your Browser:** The game will be available at [http://localhost:9002](http://localhost:9002).

## Building and Deploying to a VPS

To deploy the application to your own Virtual Private Server (VPS), you'll need to build it first and then run it on the server.

### Step 1: Build for Production

This command compiles the Next.js application into an optimized, standalone format that's ready for deployment.

```bash
npm run build
```

This process creates a `.next` directory. Because the `output: 'standalone'` option is set in `next.config.ts`, Next.js will create a special `.next/standalone` folder containing only the necessary files to run the application, including a minimal `node_modules`.

### Step 2: Transfer Files to Your VPS

Copy the following to your server. You can use tools like `scp` or `rsync`.

1.  The entire `.next/standalone` directory.
2.  The `public` directory (which contains static assets like images).

Your file structure on the server should look something like this:
```
/path/to/your/app/
├── .next/standalone/
└── public/
```

### Step 3: Run the Application on the Server

Navigate to the application directory on your VPS and start the Node.js server.

```bash
cd /path/to/your/app/.next/standalone/
node server.js
```

By default, the application will start on port `3000`. You can specify a different port using the `PORT` environment variable:
```bash
PORT=8080 node server.js
```

### Step 4: Configure Nginx as a Reverse Proxy

You'll want to use a web server like Nginx to manage incoming traffic and forward it to your running Node.js application. This is more robust and secure.

Here is a sample Nginx configuration. Create a new file in `/etc/nginx/sites-available/OrangeQuest` (the name can vary) and add the following:

```nginx
server {
    listen 80;
    server_name your_domain.com; # <-- Replace with your actual domain or IP address

    # It's highly recommended to set up HTTPS with Let's Encrypt
    # listen 443 ssl; 
    # ssl_certificate /path/to/your/fullchain.pem;
    # ssl_certificate_key /path/to/your/privkey.pem;

    location / {
        # Forward requests to your running Next.js app
        proxy_pass http://localhost:3000; # <-- Make sure this port matches the one your app is running on

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**Enable the site and restart Nginx:**

```bash
# Create a symbolic link to enable the site
sudo ln -s /etc/nginx/sites-available/OrangeQuest /etc/nginx/sites-enabled/

# Test the Nginx configuration for errors
sudo nginx -t

# If the test is successful, restart Nginx to apply the changes
sudo systemctl restart nginx
```

Your game should now be live on your VPS, accessible via your domain name.
