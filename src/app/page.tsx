import Login from '@/components/auth/Login';
import { Suspense } from 'react';
import { Rocket } from 'lucide-react';

export default function HomePage() {
  return (
    <main className="h-screen w-screen bg-background font-body text-foreground overflow-hidden flex items-center justify-center">
      <div className="text-center p-4">
        <div className="flex justify-center items-center gap-4 mb-4">
            <Rocket className="h-12 w-12 text-primary" />
            <h1 className="text-5xl font-headline text-primary">Starship Voyager</h1>
        </div>
        <p className="mb-8 text-muted-foreground max-w-md mx-auto">
          Your turn-based space exploration and combat adventure awaits. Log in to begin your journey across the stars.
        </p>
        <Suspense fallback={<div className="text-muted-foreground">Loading Login...</div>}>
          <Login />
        </Suspense>
      </div>
    </main>
  );
}
