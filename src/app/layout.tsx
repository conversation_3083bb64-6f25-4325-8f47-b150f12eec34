import type {Metadata} from 'next';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'Starship Voyager',
  description: 'A turn-based space exploration and combat game.',
};

import React from 'react';

declare global {
  interface Window {
    Bedrock: {
      BedrockPassportProvider: React.ComponentType<any>;
      LoginPanel: React.ComponentType<any>;
      useBedrockPassport: () => any;
    };
  }
}

function BedrockPassportProviderWrapper({ children }: { children: React.ReactNode }) {
  const [BedrockPassportProvider, setBedrockPassportProvider] = React.useState<React.ComponentType<any> | null>(null);

  React.useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined' && window.Bedrock && window.Bedrock.BedrockPassportProvider) {
      setBedrockPassportProvider(() => window.Bedrock.BedrockPassportProvider);
    }
  }, []);

  // During SSR, just return children without the provider
  if (typeof window === 'undefined') {
    return <>{children}</>;
  }

  if (!BedrockPassportProvider) {
    return <>{children}</>; // Return children while loading
  }

  const bedrockConfig = {
    baseUrl: 'https://api.bedrockpassport.com',
    authCallbackUrl: 'https://orangequest.merchgenieai.com/auth/callback',
    tenantId: process.env.NEXT_PUBLIC_ORANGE_ID_PROJECT_ID,
    subscriptionKey: process.env.NEXT_PUBLIC_ORANGE_ID_API_KEY,
  };

  return React.createElement(BedrockPassportProvider, bedrockConfig, children);
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;700&display=swap" rel="stylesheet" />

        <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>
        <script src="https://cdn.tailwindcss.com"></script>
      </head>
      <body className="font-body antialiased bg-background text-foreground">
        <BedrockPassportProviderWrapper>
          {children}
        </BedrockPassportProviderWrapper>
        <Toaster />
      </body>
    </html>
  );
}
