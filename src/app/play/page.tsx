'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import GameContainer from '@/components/GameContainer';
import { Rocket } from 'lucide-react';

// The bedrock library stores the user session token here
const TOKEN_STORAGE_KEY = 'bedrock-token';

export default function PlayPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // This check runs on the client-side
    const token = localStorage.getItem(TOKEN_STORAGE_KEY);
    if (!token) {
      router.push('/');
    } else {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, [router]);

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center flex-col gap-4">
        <Rocket className="h-16 w-16 animate-pulse text-primary" />
        <p className="text-xl font-headline tracking-widest">VERIFYING FLIGHT-PLAN...</p>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    // Render nothing while redirecting
    return null;
  }

  return (
    <main className="h-screen w-screen bg-background font-body text-foreground overflow-hidden">
      <GameContainer />
    </main>
  );
}
