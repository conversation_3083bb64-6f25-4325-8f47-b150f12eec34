export type Race = {
  id: 'human' | 'alien' | 'robot';
  name: string;
  description: string;
  bonus: string;
};

export type Player = {
  name:string;
  shipName: string;
  race: Race;
};

// --- CREW TYPES ---
export type CrewRole = 'Pilot' | 'Engineer' | 'Gunner' | 'Captain';

export type CrewMember = {
  id: string;
  name: string;
  role: CrewRole;
  skillLevel: number;
  description: string;
  bonusEffect: string; // A description of their gameplay effect
};


// --- SHIP & COMPONENT TYPES ---

export type ShipComponentType = 'weapon' | 'shield' | 'engine' | 'utility' | 'artifact';

export type ShipComponent = {
  id: string;
  name: string;
  description: string;
  type: ShipComponentType;
  powerDraw: number;
  mass: number;
  bonus?: { stat: string; value: number; operator: 'add' | 'multiply' };
};

export type WeaponSystem = ShipComponent & {
  type: 'weapon';
  damage: number;
  range: number; // in grid units
  projectileSpeed?: number;
  fireRate?: number;
};

export type ShieldGenerator = ShipComponent & {
  type: 'shield';
  capacity: number;
  rechargeRate: number; // units per turn
};

export type Engine = ShipComponent & {
  type: 'engine';
  thrust: number; // Determines movement range
  fuelConsumption: number; // Higher is more efficient. Fuel cost = distance / fuelConsumption.
};

export type Artifact = ShipComponent & {
    type: 'artifact';
    effectDescription: string;
};

export type Ship = {
  hull: { current: number; max: number };
  shields: { current: number; max: number };
  fuel: { current: number; max: number };
  power: { current: number; max: number };
  
  engine: Engine;
  shieldGenerator: ShieldGenerator;
  weapons: WeaponSystem[];
  crew: CrewMember[];
  
  cargo: ShipComponent[];
  upgrades: any[]; // Define upgrades later
};

export type Discovery = {
    type: 'credits' | 'artifact' | 'enemy' | 'crew' | 'outpost' | 'nothing' | 'fuel';
    value: any;
    description: string;
}

export type Planet = {
    id: string;
    systemId: string;
    name: string;
    description: string;
    image: string;
    hasBeenExplored: boolean;
    discovery: Discovery;
};

export type StarSystem = {
    id: string;
    name: string;
    x: number;
    y: number;
    size: number;
    color: string;
    planets: Planet[];
};

export type Starmap = {
    systems: StarSystem[];
}

export type GameData = {
  player: Player | null;
  ship: Ship;
  highScore: number;
  resources: {
    credits: number;
  };
  starmap: Starmap;
  currentSystemId: string | null;
  currentPlanetId: string | null;
  gameTurn: number;
  achievements: Record<string, any>;
};

export type GameState = 'character-creation' | 'starmap' | 'battle' | 'loading' | 'planet' | 'ship-management' | 'system-view';


// --- BATTLE-SPECIFIC TYPES ---

export type BattleWeapon = {
  name: string;
  damage: number;
  range: number; // in grid units
};

export type BattleShip = {
  id: string;
  name: string;
  image: string;
  health: number;
  maxHealth: number;
  shields: number;
  maxShields: number;
  weapons: BattleWeapon[];
  position: { x: number; y: number };
  isPlayer: boolean;
  movementRange: number;
  actionPoints: {
    current: number;
    max: number;
  };
};
